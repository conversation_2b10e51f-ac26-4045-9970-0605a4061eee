# Azure Blob Storage上传功能

本文档介绍如何使用AzureCNArchaeologist项目的Azure Blob Storage上传功能，将导出的JSON文件上传到Azure存储账户，以便与下游CMS团队进行数据交流。

## 功能概述

- 🔄 **批量上传**: 自动上传output文件夹中的所有JSON文件
- 📁 **分类组织**: 按产品分类自动组织文件结构
- 🔍 **试运行模式**: 支持预览上传操作而不实际执行
- 📋 **文件列表**: 查看已上传到Blob Storage的文件
- 🏷️ **自定义前缀**: 支持自定义Blob名称前缀

## 环境配置

### 1. 安装依赖

项目已自动安装所需依赖：
- `azure-storage-blob`: Azure Blob Storage客户端
- `python-dotenv`: 环境变量管理

### 2. 配置环境变量

在项目根目录的`.env`文件中配置Azure Storage连接信息：

```bash
# Azure Storage Account配置
# 请将下面的连接字符串替换为CMS团队提供的实际连接字符串
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your_account_name;AccountKey=your_account_key;EndpointSuffix=core.windows.net

# Blob容器名称（可选，默认为 'cms-json-files'）
AZURE_BLOB_CONTAINER_NAME=cms-json-files
```

**重要**: 请将`your_account_name`和`your_account_key`替换为CMS团队提供的实际值。

## 使用方法

### 方式一：使用CLI命令（推荐）

#### 1. 试运行模式（预览上传）
```bash
python cli.py upload --dry-run
```

#### 2. 上传所有JSON文件
```bash
python cli.py upload
```

#### 3. 指定输出目录和前缀
```bash
python cli.py upload --output-dir output --prefix cms-data
```

#### 4. 列出已上传的文件
```bash
python cli.py upload --list
```

#### 5. 列出指定前缀的文件
```bash
python cli.py upload --list --prefix cms-data
```

### 方式二：直接使用上传脚本

#### 1. 试运行模式
```bash
python scripts/upload_to_blob.py upload --dry-run
```

#### 2. 上传文件
```bash
python scripts/upload_to_blob.py upload --output-dir output --prefix cms
```

#### 3. 列出文件
```bash
python scripts/upload_to_blob.py list --prefix cms
```

## 文件组织结构

上传到Blob Storage的文件将按以下结构组织：

```
容器根目录/
├── [前缀]/                    # 可选的自定义前缀
│   ├── ai/                    # AI相关产品
│   │   ├── anomaly-detector_flexible_content_20250827_100447.json
│   │   ├── form-recognizer_flexible_content_20250827_103505.json
│   │   └── ...
│   ├── compute/               # 计算相关产品
│   │   ├── app-service_flexible_content_20250827_141859.json
│   │   └── ...
│   ├── networking/            # 网络相关产品
│   │   ├── application-gateway_flexible_content_20250827_113859.json
│   │   └── ...
│   └── ...
```

## 输出示例

### 试运行模式输出
```
📁 找到 28 个JSON文件
📊 文件分布:
  ai: 4 个文件
  compute: 2 个文件
  container: 1 个文件
  integration: 3 个文件
  iot: 1 个文件
  management: 2 个文件
  migration: 2 个文件
  networking: 10 个文件
  websites: 3 个文件
🔍 试运行模式，不会实际上传文件
```

### 实际上传输出
```
✅ 已连接到Azure Blob Storage容器: cms-json-files
📤 上传完成: 28 成功, 0 失败
✅ 成功上传的文件:
  output/ai/anomaly-detector_flexible_content_20250827_100447.json -> ai/anomaly-detector_flexible_content_20250827_100447.json
  output/compute/app-service_flexible_content_20250827_141859.json -> compute/app-service_flexible_content_20250827_141859.json
  ...
```

## 错误处理

### 常见错误及解决方案

1. **连接字符串未配置**
   ```
   ❌ Azure Storage连接字符串未配置
   请在.env文件中设置AZURE_STORAGE_CONNECTION_STRING
   ```
   **解决**: 检查`.env`文件中的`AZURE_STORAGE_CONNECTION_STRING`配置

2. **容器不存在**
   - 脚本会自动创建容器，如果创建失败请检查权限

3. **文件上传失败**
   - 检查网络连接
   - 验证Azure Storage账户权限
   - 确认文件未被其他程序占用

## CMS团队访问方式

CMS团队可以通过以下方式访问上传的JSON文件：

1. **Azure Portal**: 登录Azure Portal，导航到存储账户
2. **Azure Storage Explorer**: 使用Microsoft Azure Storage Explorer工具
3. **直接URL访问**: 使用生成的Blob URL（如果容器为公共访问）
4. **SAS URL**: 使用共享访问签名URL（推荐用于临时访问）

## 最佳实践

1. **定期清理**: 建议定期清理旧的JSON文件以节省存储空间
2. **命名规范**: 使用有意义的前缀来组织不同批次的数据
3. **权限管理**: 确保只有授权人员可以访问存储账户
4. **监控使用**: 定期检查存储使用情况和访问日志

## 故障排除

如果遇到问题，请检查：

1. `.env`文件配置是否正确
2. 网络连接是否正常
3. Azure Storage账户是否有效
4. 文件权限是否正确
5. 查看日志文件：`logs/app.log`

## 技术支持

如需技术支持，请联系项目维护团队或查看项目文档。
