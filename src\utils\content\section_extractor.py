#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门section提取器
抽离Banner、Description、QA的具体提取逻辑，支持flexible JSON的commonSections格式
"""

import sys
import copy
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.core.logging import get_logger
from src.utils.html.cleaner import clean_html_content

logger = get_logger(__name__)


class SectionExtractor:
    """专门section提取器 - 提取Banner、Description、QA等特定section内容"""

    def __init__(self):
        """初始化section提取器"""
        logger.info("🔧 初始化SectionExtractor")

    def extract_all_sections(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """
        提取所有commonSections
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            commonSections列表，每个元素包含sectionType和content
        """
        logger.info("🔍 提取所有commonSections...")
        
        sections = []
        
        # 1. 提取Banner
        banner_content = self.extract_banner(soup)
        if banner_content:
            sections.append({
                "sectionType": "Banner",
                "sectionTitle": "",  # Banner通常无标题
                "content": banner_content,
                "sortOrder": 1,
                "isActive": True
            })
        
        # 2. 提取Description
        description_content = self.extract_description(soup)
        if description_content:
            sections.append({
                "sectionType": "ProductDescription",
                "sectionTitle": "",  # Description通常无标题
                "content": description_content,
                "sortOrder": 1,
                "isActive": True
            })
        
        # 3. 提取QA
        qa_content = self.extract_qa(soup)
        if qa_content:
            sections.append({
                "sectionType": "Qa",
                "sectionTitle": "",  # QA通常内嵌标题
                "content": qa_content,
                "sortOrder": 1,
                "isActive": True
            })
        
        logger.info(f"✓ 提取了 {len(sections)} 个完整commonSections")
        return sections

    def extract_banner(self, soup: BeautifulSoup) -> str:
        """
        提取Banner内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            Banner HTML内容字符串
        """
        logger.info("🎨 提取Banner内容...")
        
        try:
            # 寻找常见的banner选择器
            banner_selectors = [
                'div.common-banner',
                'div.common-banner-image', 
                '.banner',
                '.hero',
                '.page-banner',
                '.product-banner'
            ]
            
            for selector in banner_selectors:
                banner = soup.select_one(selector)
                if banner:
                    # 图片路径已由ExtractionCoordinator中的preprocess_image_paths全局处理
                    logger.info(f"✓ 找到Banner内容，选择器: {selector}")
                    return clean_html_content(str(banner))
            
            logger.info("⚠ 未找到Banner内容")
            return ""
            
        except Exception as e:
            logger.info(f"⚠ Banner内容提取失败: {e}")
            return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """
        提取描述内容
        Banner后第一个有效描述元素的内容（支持pricing-page-section、ul等元素），但排除FAQ
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            描述内容HTML字符串
        """
        logger.info("📝 提取描述内容...")
        
        try:
            # 首先查找Banner元素
            banner = soup.find('div', {'class': ['common-banner', 'col-top-banner']})
            if banner:
                # 从Banner后面查找第一个有效的描述元素
                current = banner
                while current:
                    current = current.find_next_sibling()
                    if current and current.name in ['div', 'ul', 'ol', 'section']:
                        # 检查是否是pricing-page-section
                        if current.name == 'div' and 'pricing-page-section' in current.get('class', []):
                            content_text = current.get_text().strip()
                            # 检查是否是FAQ内容(包含more-detail或支持和服务级别协议)
                            if ('more-detail' in str(current) or 
                                '支持和服务级别协议' in content_text or
                                '常见问题' in content_text):
                                continue  # 跳过FAQ内容，查找下一个section
                            
                            # 找到合适的描述section
                            clean_content = clean_html_content(str(current))
                            logger.info(f"✓ 找到pricing-page-section描述内容，长度: {len(clean_content)}")
                            return clean_content
                            
                        # 检查是否是ul/ol等描述元素
                        elif current.name in ['ul', 'ol']:
                            # 检查是否包含描述性内容（避免导航菜单）
                            content_text = current.get_text().strip()
                            if (len(content_text) > 50 and  # 内容足够长
                                not any(nav_indicator in content_text.lower() for nav_indicator in [
                                    '导航', 'menu', 'nav', '首页', 'home'
                                ]) and
                                not any(faq_indicator in content_text for faq_indicator in [
                                    '常见问题', 'faq', '支持和服务级别协议'
                                ])):
                                clean_content = clean_html_content(str(current))
                                logger.info(f"✓ 找到{current.name}描述内容，长度: {len(clean_content)}")
                                return clean_content
                        
                        # 检查是否是其他描述容器
                        elif (current.name == 'div' and 
                              current.get('class') and 
                              any(desc_class in current.get('class', []) for desc_class in [
                                  'description', 'intro', 'summary', 'overview'
                              ])):
                            content_text = current.get_text().strip()
                            if (len(content_text) > 30 and
                                not any(faq_indicator in content_text for faq_indicator in [
                                    '常见问题', 'faq', '支持和服务级别协议'  
                                ])):
                                clean_content = clean_html_content(str(current))
                                logger.info(f"✓ 找到描述容器内容，长度: {len(clean_content)}")
                                return clean_content
            
            # 备用方案：尝试传统选择器
            desc_selectors = [
                '.description',
                '.product-description', 
                '.intro',
                '.summary',
                'section.overview',
                'ul.product-features',  # 添加对产品特性列表的支持
                'ol.product-features'
            ]
            
            for selector in desc_selectors:
                element = soup.select_one(selector)
                if element:
                    content_text = element.get_text().strip()
                    # 确保内容足够长且不是FAQ
                    if (len(content_text) > 30 and
                        not any(faq_indicator in content_text for faq_indicator in [
                            '常见问题', 'faq', '支持和服务级别协议'
                        ])):
                        clean_content = clean_html_content(str(element))
                        logger.info(f"✓ 使用备用描述选择器: {selector}")
                        return clean_content
            
            logger.info("⚠ 未找到描述内容")
            return ""
            
        except Exception as e:
            logger.info(f"⚠ 描述内容提取失败: {e}")
            return ""

    def extract_qa(self, soup: BeautifulSoup) -> str:
        """
        提取Q&A内容以及支持和服务级别协议内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            Q&A内容HTML字符串
        """
        logger.info("❓ 提取Q&A内容...")
        
        try:
            qa_content = ""
            
            # # 1. 查找标准FAQ容器
            # faq_containers = [
            #     soup.find('div', class_='faq'),
            #     soup.find('div', class_='qa'),
            #     soup.find('section', class_='faq'),
            #     soup.find('section', class_='qa')
            # ]
            #
            # for container in faq_containers:
            #     if container:
            #         qa_content += str(container)
                    
            # 1. 查找more-detail容器
            more_detail_containers = soup.find_all('div', class_='more-detail')
            for container in more_detail_containers:
                if container:
                    qa_content += str(container)
                    logger.info(f"✓ 找到more-detail容器")
            
            # # 3. 查找包含FAQ结构的列表
            # faq_lists = soup.find_all('ul', class_='faq-list')
            # for faq_list in faq_lists:
            #     if faq_list:
            #         qa_content += str(faq_list)
            
            # 4. 查找pricing-page-section中的支持、SLA和其他信息内容
            pricing_sections = soup.find_all('div', class_='pricing-page-section')
            for section in pricing_sections:
                section_text = section.get_text().lower()
                # 提取支持和SLA部分
                if '支持和服务级别协议' in section_text or 'sla' in section_text:
                    qa_content += str(section)
                    logger.info(f"✓ 找到pricing-page-section支持/SLA内容")
                # 提取其他信息部分（归类为FAQ）
                elif ('其他信息' in section_text or
                      '更多信息' in section_text or
                      '相关信息' in section_text or
                      '重要信息' in section_text or
                      '注意事项' in section_text):
                    qa_content += str(section)
                    logger.info(f"✓ 找到pricing-page-section其他信息内容（归类为FAQ）")
            
            # # 5. 查找accordion-style的FAQ
            # accordion_items = soup.find_all(['div', 'section'], class_=['accordion-item', 'faq-item'])
            # for item in accordion_items:
            #     if item:
            #         qa_content += str(item)
            #
            # 清理QA内容
            if qa_content:
                clean_qa = clean_html_content(qa_content)
                logger.info(f"✓ 提取了 {len(clean_qa)} 字符的Q&A内容")
                return clean_qa
            else:
                logger.info("⚠ 未找到Q&A内容")
                return ""
            
        except Exception as e:
            logger.info(f"⚠ Q&A内容提取失败: {e}")
