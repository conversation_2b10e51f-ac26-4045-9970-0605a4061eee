{"version": "2.1", "last_updated": "2025-09-08", "total_products": 87, "future_capacity": 120, "categories": {"ai-ml": {"count": 6, "config_path": "products/ai-ml/", "products": ["anomaly-detector", "search", "machine-learning", "databricks", "form-recognizer", "metrics-advisor"]}, "management": {"count": 8, "config_path": "products/management/", "products": ["automation", "backup", "scheduler", "monitor", "azure-policy", "advisor", "azure-firewall", "azure-update-management-center"]}, "migration": {"count": 2, "config_path": "products/migration/", "products": ["azure-migrate", "site-recovery"]}, "database": {"count": 12, "config_path": "products/database/", "products": ["managed-instance", "sql-database", "synapse-analytics", "sql-server-stretch-database", "cosmos-db", "cache", "mysql", "postgresql", "ma<PERSON>b", "data-explorer", "database-migration", "sql-edge"]}, "security": {"count": 3, "config_path": "products/security/", "products": ["azure-defender", "key-vault", "microsoft-sentinel"]}, "integration": {"count": 3, "config_path": "products/integration/", "products": ["api-management", "event-grid", "service-bus"]}, "compute": {"count": 8, "config_path": "products/compute/", "products": ["virtual-machine-scale-sets", "app-service", "batch", "cloud-services", "azure-functions", "dedicated-host", "spring-cloud", "hpc-cache"], "large_html_products": []}, "iot": {"count": 5, "config_path": "products/iot/", "products": ["iot-hub", "iot-edge", "event-hubs", "logic-apps", "time-series-insights"]}, "identity": {"count": 3, "config_path": "products/identity/", "products": ["active-directory-b2c", "active-directory-ds", "multi-factor-authentication"]}, "analysis": {"count": 7, "config_path": "products/analysis/", "products": ["purview", "hdinsight", "stream-analytics", "power-bi-embedded", "analysis-services", "ssis", "data-pipeline"], "large_html_products": []}, "networking": {"count": 13, "config_path": "products/networking/", "products": ["application-gateway", "azure-nat-gateway", "core-control-plane", "dns", "ip-addresses", "load-balancer", "network-watcher", "route-server", "traffic-manager", "virtual-network", "virtual-network-manager", "virtual-wan", "vpn-gateway"]}, "storage": {"count": 2, "config_path": "products/storage/", "products": ["storage-files", "data-lake-storage"]}, "container": {"count": 5, "config_path": "products/container/", "products": ["container-apps", "container-instances", "container-registry", "kubernetes-service", "service-fabric"]}, "websites": {"count": 5, "config_path": "products/websites/", "products": ["notification-hubs", "frontdoor", "signalr-service", "fluid-relay", "web-pubsub"]}, "dev-tools": {"count": 1, "config_path": "products/dev-tools/", "products": ["app-configuration"]}, "hybrid-multicloud": {"count": 2, "config_path": "products/hybrid-multicloud/", "products": ["hci", "hub"]}, "azure-virtual-desptop": {"count": 1, "config_path": "products/azure-virtual-desktop/", "products": ["virtual-desktop"]}, "dev-ops": {"count": 1, "config_path": "products/dev-ops/", "products": ["managed-g<PERSON><PERSON><PERSON>"]}}, "load_strategy": {"lazy_load": true, "cache_configs": true, "parallel_load": false, "cache_ttl_minutes": 30}, "processing_defaults": {"memory_limit_mb": 512, "timeout_seconds": 120, "chunk_size_kb": 1024, "large_file_threshold_mb": 2}}